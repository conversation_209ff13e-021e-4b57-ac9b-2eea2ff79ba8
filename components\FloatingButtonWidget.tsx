"use client"

import type React from "react"

import { useState, useRef, useEffect } from "react"
import {
  MessageCircle,
  X,
  Send,
  User,
  Bot,
  Sparkles,
  Heart,
  Phone,
  PhoneOff,
  Mic,
  MicOff,
  Volume2,
  VolumeX,
  Video,
  VideoOff,
} from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { cn } from "@/lib/utils"

interface Message {
  id: string
  content: string
  sender: "user" | "ai"
  timestamp: Date
}

export function FloatingButtonWidget() {
  const [isOptionsOpen, setIsOptionsOpen] = useState(false)
  const [activeDialog, setActiveDialog] = useState<"chat" | "call" | null>(null)
  const [messages, setMessages] = useState<Message[]>([
    {
      id: "1",
      content: "Hello! ✨ I'm your AI assistant. I'm here to help you with anything you need!",
      sender: "ai",
      timestamp: new Date(),
    },
  ])
  const [inputValue, setInputValue] = useState("")
  const [isTyping, setIsTyping] = useState(false)
  const [isConnected, setIsConnected] = useState(false)
  const [isMuted, setIsMuted] = useState(false)
  const [isSpeakerOn, setIsSpeakerOn] = useState(true)
  const [isVideoOn, setIsVideoOn] = useState(false)
  const [callDuration, setCallDuration] = useState(0)
  const [isAISpeaking, setIsAISpeaking] = useState(false)
  const messagesEndRef = useRef<HTMLDivElement>(null)

  useEffect(() => {
    let interval: NodeJS.Timeout
    if (isConnected) {
      interval = setInterval(() => {
        setCallDuration((prev) => prev + 1)
      }, 1000)
    }
    return () => clearInterval(interval)
  }, [isConnected])

  useEffect(() => {
    if (isConnected) {
      const speakingInterval = setInterval(
        () => {
          setIsAISpeaking(true)
          setTimeout(() => setIsAISpeaking(false), 2000 + Math.random() * 3000)
        },
        5000 + Math.random() * 5000,
      )
      return () => clearInterval(speakingInterval)
    }
  }, [isConnected])

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" })
  }

  useEffect(() => {
    scrollToBottom()
  }, [messages])

  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60)
    const secs = seconds % 60
    return `${mins.toString().padStart(2, "0")}:${secs.toString().padStart(2, "0")}`
  }

  const handleMainButtonClick = () => {
    if (activeDialog) {
      setActiveDialog(null)
      setIsOptionsOpen(false)
    } else {
      setIsOptionsOpen(!isOptionsOpen)
    }
  }

  const handleChatClick = () => {
    setActiveDialog("chat")
    setIsOptionsOpen(false)
  }

  const handleCallClick = () => {
    setActiveDialog("call")
    setIsOptionsOpen(false)
  }

  const handleCloseDialog = () => {
    setActiveDialog(null)
    setIsOptionsOpen(false)
    if (isConnected) {
      setIsConnected(false)
      setCallDuration(0)
      setIsAISpeaking(false)
    }
  }

  const handleSendMessage = async () => {
    if (!inputValue.trim()) return

    const userMessage: Message = {
      id: Date.now().toString(),
      content: inputValue,
      sender: "user",
      timestamp: new Date(),
    }

    setMessages((prev) => [...prev, userMessage])
    setInputValue("")
    setIsTyping(true)

    setTimeout(
      () => {
        const responses = [
          "That's really interesting! Tell me more about that. 🤔",
          "I love discussing this topic! What's your perspective? 💭",
          "Great question! Let me think about that for a moment... ✨",
          "You've got a great point there! Here's what I think... 🌟",
          "That's fascinating! I'd love to explore this further with you. 🚀",
        ]
        const aiMessage: Message = {
          id: (Date.now() + 1).toString(),
          content: responses[Math.floor(Math.random() * responses.length)],
          sender: "ai",
          timestamp: new Date(),
        }
        setMessages((prev) => [...prev, aiMessage])
        setIsTyping(false)
      },
      1500 + Math.random() * 2000,
    )
  }

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === "Enter" && !e.shiftKey) {
      e.preventDefault()
      handleSendMessage()
    }
  }

  const handleStartCall = () => {
    setIsConnected(true)
    setCallDuration(0)
  }

  const handleEndCall = () => {
    setIsConnected(false)
    setCallDuration(0)
    setIsAISpeaking(false)
  }

  return (
    <>
      {/* Main Dialog */}
      {activeDialog && (
        <div className="fixed inset-0 z-[90] flex items-center justify-end p-4">
          <div className="w-full max-w-sm h-full bg-gradient-to-br from-purple-900 via-blue-900 to-indigo-900 rounded-3xl shadow-2xl border border-purple-500/20 flex flex-col overflow-hidden animate-in fade-in-0 slide-in-from-right-5 duration-300">
            {/* Header */}
            <div className="relative bg-gradient-to-r from-pink-600 via-purple-600 to-blue-600 p-4 text-white">
              <div className="absolute inset-0 bg-gradient-to-r from-pink-600/90 via-purple-600/90 to-blue-600/90 backdrop-blur-sm" />
              <div className="relative flex items-center justify-between">
                <div className="flex items-center space-x-3">
                  <div className="w-10 h-10 bg-white/20 rounded-full flex items-center justify-center backdrop-blur-sm">
                    {activeDialog === "chat" ? (
                      <Sparkles className="w-5 h-5 text-white animate-pulse" />
                    ) : (
                      <div className="text-lg">🤖</div>
                    )}
                  </div>
                  <div>
                    <h2 className="text-lg font-bold">{activeDialog === "chat" ? "AI Assistant" : "Voice Call"}</h2>
                    <p className="text-white/80 text-sm">
                      {activeDialog === "chat"
                        ? "Always here to help ✨"
                        : isConnected
                          ? `Connected • ${formatTime(callDuration)}`
                          : "Ready to connect"}
                    </p>
                  </div>
                </div>
                <Button
                  onClick={handleCloseDialog}
                  variant="ghost"
                  size="icon"
                  className="w-8 h-8 rounded-full hover:bg-white/20 text-white"
                >
                  <X className="w-4 h-4" />
                </Button>
              </div>

              {/* Floating particles in header */}
              <div className="absolute inset-0 overflow-hidden pointer-events-none">
                {[...Array(6)].map((_, i) => (
                  <div
                    key={i}
                    className="absolute w-1 h-1 bg-white/40 rounded-full animate-float"
                    style={{
                      left: `${Math.random() * 100}%`,
                      top: `${Math.random() * 100}%`,
                      animationDelay: `${Math.random() * 3}s`,
                      animationDuration: `${2 + Math.random() * 2}s`,
                    }}
                  />
                ))}
              </div>
            </div>

            {/* Content */}
            {activeDialog === "chat" ? (
              <>
                {/* Chat Messages */}
                <div className="flex-1 overflow-y-auto p-4 space-y-3 bg-gradient-to-b from-transparent via-purple-900/20 to-blue-900/30">
                  {messages.map((message, index) => (
                    <div
                      key={message.id}
                      className={cn(
                        "flex items-end space-x-2 animate-in slide-in-from-bottom-2 duration-300",
                        message.sender === "user" ? "justify-end" : "justify-start",
                      )}
                      style={{ animationDelay: `${index * 100}ms` }}
                    >
                      {message.sender === "ai" && (
                        <div className="w-6 h-6 bg-gradient-to-r from-pink-500 to-purple-500 rounded-full flex items-center justify-center flex-shrink-0 shadow-lg">
                          <Bot className="w-3 h-3 text-white" />
                        </div>
                      )}

                      <div
                        className={cn(
                          "max-w-[75%] p-3 rounded-2xl shadow-lg backdrop-blur-sm transition-all duration-300 hover:scale-[1.02]",
                          message.sender === "user"
                            ? "bg-gradient-to-r from-blue-600 to-purple-600 text-white rounded-br-md border border-blue-400/30"
                            : "bg-white/10 text-white rounded-bl-md border border-white/20",
                        )}
                      >
                        <p className="text-sm leading-relaxed">{message.content}</p>
                        <p className="text-xs mt-1 opacity-70 text-white/80">
                          {message.timestamp.toLocaleTimeString([], {
                            hour: "2-digit",
                            minute: "2-digit",
                          })}
                        </p>
                      </div>

                      {message.sender === "user" && (
                        <div className="w-6 h-6 bg-gradient-to-r from-blue-500 to-cyan-500 rounded-full flex items-center justify-center flex-shrink-0 shadow-lg">
                          <User className="w-3 h-3 text-white" />
                        </div>
                      )}
                    </div>
                  ))}

                  {isTyping && (
                    <div className="flex items-end space-x-2 animate-in slide-in-from-bottom-2 duration-300">
                      <div className="w-6 h-6 bg-gradient-to-r from-pink-500 to-purple-500 rounded-full flex items-center justify-center flex-shrink-0 shadow-lg">
                        <Bot className="w-3 h-3 text-white" />
                      </div>
                      <div className="bg-white/10 backdrop-blur-sm p-3 rounded-2xl rounded-bl-md shadow-lg border border-white/20">
                        <div className="flex space-x-1">
                          <div className="w-2 h-2 bg-pink-400 rounded-full animate-bounce" />
                          <div className="w-2 h-2 bg-purple-400 rounded-full animate-bounce animation-delay-200" />
                          <div className="w-2 h-2 bg-blue-400 rounded-full animate-bounce animation-delay-400" />
                        </div>
                      </div>
                    </div>
                  )}
                  <div ref={messagesEndRef} />
                </div>

                {/* Chat Input */}
                <div className="p-4 bg-gradient-to-r from-purple-900/50 via-blue-900/50 to-indigo-900/50 backdrop-blur-sm border-t border-white/10">
                  <div className="flex space-x-3 items-end">
                    <div className="flex-1 relative">
                      <Input
                        value={inputValue}
                        onChange={(e) => setInputValue(e.target.value)}
                        onKeyPress={handleKeyPress}
                        placeholder="Type your message..."
                        className="pr-10 bg-white/10 backdrop-blur-sm border-white/20 focus:border-pink-300 focus:ring-pink-200 rounded-2xl shadow-lg text-white placeholder:text-white/60"
                        disabled={isTyping}
                      />
                      <div className="absolute right-3 top-1/2 -translate-y-1/2">
                        <Heart className="w-4 h-4 text-pink-400" />
                      </div>
                    </div>
                    <Button
                      onClick={handleSendMessage}
                      disabled={!inputValue.trim() || isTyping}
                      className="w-10 h-10 rounded-2xl bg-gradient-to-r from-pink-600 to-purple-600 hover:from-pink-700 hover:to-purple-700 shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-105"
                      size="icon"
                    >
                      <Send className="w-4 h-4" />
                    </Button>
                  </div>
                </div>
              </>
            ) : (
              <>
                {/* Call Interface */}
                <div className="flex-1 flex items-center justify-center p-6 bg-gradient-to-br from-purple-900/20 via-blue-900/20 to-indigo-900/20">
                  <div className="relative">
                    {/* Main Avatar Circle */}
                    <div
                      className={cn(
                        "w-32 h-32 rounded-full transition-all duration-500 flex items-center justify-center relative overflow-hidden",
                        isConnected
                          ? isAISpeaking
                            ? "bg-gradient-to-r from-green-400 via-blue-500 to-purple-600 animate-pulse scale-110 shadow-2xl shadow-purple-500/50"
                            : "bg-gradient-to-r from-blue-500 via-purple-600 to-pink-600 shadow-xl shadow-purple-500/30"
                          : "bg-gradient-to-r from-gray-600 to-gray-700 shadow-lg",
                      )}
                    >
                      {/* Animated rings */}
                      {isConnected && (
                        <>
                          <div className="absolute inset-0 rounded-full border-2 border-white/30 animate-ping" />
                          <div className="absolute inset-4 rounded-full border border-white/20 animate-pulse" />
                        </>
                      )}

                      {/* AI Icon */}
                      <div className="relative z-10">
                        <div className="w-12 h-12 bg-white/20 rounded-full flex items-center justify-center backdrop-blur-sm">
                          <div className="text-2xl">🤖</div>
                        </div>
                      </div>

                      {/* Floating particles */}
                      {isConnected && (
                        <div className="absolute inset-0 overflow-hidden">
                          {[...Array(8)].map((_, i) => (
                            <div
                              key={i}
                              className="absolute w-1 h-1 bg-white/40 rounded-full animate-float"
                              style={{
                                left: `${Math.random() * 100}%`,
                                top: `${Math.random() * 100}%`,
                                animationDelay: `${Math.random() * 3}s`,
                                animationDuration: `${2 + Math.random() * 2}s`,
                              }}
                            />
                          ))}
                        </div>
                      )}
                    </div>

                    {/* Status Text */}
                    <div className="text-center mt-4 space-y-1">
                      <div className="text-white text-lg font-semibold">
                        {isConnected
                          ? isAISpeaking
                            ? "AI is speaking..."
                            : "Connected & Listening"
                          : "Ready to connect"}
                      </div>
                      <div className="text-white/60 text-sm">
                        {isConnected ? "Tap to interact" : "Start your conversation"}
                      </div>
                    </div>
                  </div>
                </div>

                {/* Call Controls */}
                <div className="p-4 space-y-4 bg-gradient-to-r from-purple-900/50 via-blue-900/50 to-indigo-900/50 backdrop-blur-sm border-t border-white/10">
                  {/* Secondary Controls */}
                  {isConnected && (
                    <div className="flex justify-center space-x-3">
                      <Button
                        onClick={() => setIsMuted(!isMuted)}
                        variant="ghost"
                        size="icon"
                        className={cn(
                          "w-10 h-10 rounded-full transition-all duration-300",
                          isMuted
                            ? "bg-red-500/20 text-red-400 hover:bg-red-500/30"
                            : "bg-white/10 text-white hover:bg-white/20",
                        )}
                      >
                        {isMuted ? <MicOff className="w-4 h-4" /> : <Mic className="w-4 h-4" />}
                      </Button>

                      <Button
                        onClick={() => setIsSpeakerOn(!isSpeakerOn)}
                        variant="ghost"
                        size="icon"
                        className={cn(
                          "w-10 h-10 rounded-full transition-all duration-300",
                          !isSpeakerOn
                            ? "bg-yellow-500/20 text-yellow-400 hover:bg-yellow-500/30"
                            : "bg-white/10 text-white hover:bg-white/20",
                        )}
                      >
                        {isSpeakerOn ? <Volume2 className="w-4 h-4" /> : <VolumeX className="w-4 h-4" />}
                      </Button>

                      <Button
                        onClick={() => setIsVideoOn(!isVideoOn)}
                        variant="ghost"
                        size="icon"
                        className={cn(
                          "w-10 h-10 rounded-full transition-all duration-300",
                          isVideoOn
                            ? "bg-blue-500/20 text-blue-400 hover:bg-blue-500/30"
                            : "bg-white/10 text-white hover:bg-white/20",
                        )}
                      >
                        {isVideoOn ? <Video className="w-4 h-4" /> : <VideoOff className="w-4 h-4" />}
                      </Button>
                    </div>
                  )}

                  {/* Main Call Button */}
                  <div className="flex justify-center">
                    {!isConnected ? (
                      <Button
                        onClick={handleStartCall}
                        className="w-14 h-14 rounded-full bg-gradient-to-r from-green-500 to-emerald-600 hover:from-green-600 hover:to-emerald-700 shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-110"
                        size="icon"
                      >
                        <Phone className="w-6 h-6" />
                      </Button>
                    ) : (
                      <Button
                        onClick={handleEndCall}
                        className="w-14 h-14 rounded-full bg-gradient-to-r from-red-500 to-rose-600 hover:from-red-600 hover:to-rose-700 shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-110"
                        size="icon"
                      >
                        <PhoneOff className="w-6 h-6" />
                      </Button>
                    )}
                  </div>
                </div>
              </>
            )}
          </div>
        </div>
      )}

      {/* Floating Button */}
      <div className="fixed bottom-6 right-6 z-[100]">
        <Button
          onClick={handleMainButtonClick}
          className={cn(
            "w-14 h-14 rounded-full text-white shadow-lg hover:scale-105 transition-all duration-300",
            activeDialog
              ? "bg-gray-600 hover:bg-gray-700"
              : "bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700",
          )}
          size="icon"
        >
          {activeDialog ? <X className="w-6 h-6" /> : <MessageCircle className="w-6 h-6" />}
        </Button>

        {/* Options Menu */}
        <div
          className={cn(
            "absolute bottom-16 right-0 flex flex-col gap-3 transition-all duration-300",
            isOptionsOpen && !activeDialog
              ? "opacity-100 translate-y-0 pointer-events-auto"
              : "opacity-0 translate-y-4 pointer-events-none",
          )}
        >
          <Button
            onClick={handleChatClick}
            className="px-6 py-3 rounded-full bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white shadow-lg hover:-translate-x-2 transition-all duration-300 min-w-[100px]"
          >
            Chat
          </Button>
          <Button
            onClick={handleCallClick}
            className="px-6 py-3 rounded-full bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700 text-white shadow-lg hover:-translate-x-2 transition-all duration-300 min-w-[100px]"
          >
            Call
          </Button>
        </div>
      </div>
    </>
  )
}
