"use client"

import { useState, useCallback } from "react"

type ConversationStatus = "disconnected" | "connecting" | "connected" | "error"

interface ConversationMessage {
  type: "text" | "audio"
  data: string
}

interface UseConversationProps {
  onConnect?: () => void
  onDisconnect?: () => void
  onError?: (error: any) => void
  onMessage?: (message: ConversationMessage) => void
}

interface UseConversationReturn {
  status: ConversationStatus
  isSpeaking: boolean
  startSession: (options: { signedUrl: string }) => Promise<string | undefined>
  endSession: () => Promise<void>
  sendMessage: (message: string) => void
}

export function useConversation({
  onConnect,
  onDisconnect,
  onError,
  onMessage,
}: UseConversationProps): UseConversationReturn {
  const [status, setStatus] = useState<ConversationStatus>("disconnected")
  const [isSpeaking, setIsSpeaking] = useState<boolean>(false)

  const startSession = useCallback(
    async (options: { signedUrl: string }) => {
      setStatus("connecting")
      // Simulate connection
      await new Promise((resolve) => setTimeout(resolve, 1000))
      setStatus("connected")
      setIsSpeaking(false)

      // Simulate speaking after connection
      setTimeout(() => {
        setIsSpeaking(true)
        setTimeout(() => setIsSpeaking(false), 3000)
      }, 2000)

      onConnect?.()
      return "simulated-conversation-id"
    },
    [onConnect],
  )

  const endSession = useCallback(async () => {
    setStatus("disconnected")
    setIsSpeaking(false)
    onDisconnect?.()
  }, [onDisconnect])

  const sendMessage = useCallback((message: string) => {
    console.log("Sending message:", message)
  }, [])

  return {
    status,
    isSpeaking,
    startSession,
    endSession,
    sendMessage,
  }
}
