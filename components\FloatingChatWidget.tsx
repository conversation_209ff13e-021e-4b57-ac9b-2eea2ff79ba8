"use client"

import type React from "react"

import { useState, useRef, useEffect } from "react"
import { MessageCircle, X, Send, Mic, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON> } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { cn } from "@/lib/utils"
import { useConversation } from "@11labs/react"

interface Message {
  id: string
  content: string
  sender: "user" | "ai"
  timestamp: Date
}

async function requestMicrophonePermission() {
  try {
    await navigator.mediaDevices.getUserMedia({ audio: true })
    return true
  } catch {
    console.error("Microphone permission denied")
    return false
  }
}

async function getSignedUrl(): Promise<string> {
  const response = await fetch("/api/signed-url")
  if (!response.ok) {
    throw Error("Failed to get signed url")
  }
  const data = await response.json()
  return data.signedUrl
}

export function FloatingChatWidget() {
  const [isOptionsOpen, setIsOptionsOpen] = useState(false)
  const [isVoiceCallOpen, setIsVoiceCallOpen] = useState(false)
  const [isTextChatOpen, setIsTextChatOpen] = useState(false)
  const [messages, setMessages] = useState<Message[]>([
    {
      id: "1",
      content: "Hello! I'm your AI assistant. How can I help you today?",
      sender: "ai",
      timestamp: new Date(),
    },
  ])
  const [inputValue, setInputValue] = useState("")
  const [isTyping, setIsTyping] = useState(false)
  const messagesEndRef = useRef<HTMLDivElement>(null)

  const conversation = useConversation({
    onConnect: () => {
      console.log("Voice call connected")
    },
    onDisconnect: () => {
      console.log("Voice call disconnected")
    },
    onError: (error) => {
      console.log(error)
      alert("An error occurred during the voice conversation")
    },
    onMessage: (message) => {
      console.log(message)
    },
  })

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" })
  }

  useEffect(() => {
    scrollToBottom()
  }, [messages])

  const handleMainButtonClick = () => {
    setIsOptionsOpen(!isOptionsOpen)
  }

  const handleVoiceCallClick = () => {
    setIsVoiceCallOpen(true)
    setIsOptionsOpen(false)
    setIsTextChatOpen(false)
  }

  const handleTextChatClick = () => {
    setIsTextChatOpen(true)
    setIsOptionsOpen(false)
    setIsVoiceCallOpen(false)
  }

  const handleCloseVoiceCall = () => {
    setIsVoiceCallOpen(false)
    if (conversation.status === "connected") {
      conversation.endSession()
    }
  }

  const handleCloseTextChat = () => {
    setIsTextChatOpen(false)
  }

  const handleSendMessage = async () => {
    if (!inputValue.trim()) return

    const userMessage: Message = {
      id: Date.now().toString(),
      content: inputValue,
      sender: "user",
      timestamp: new Date(),
    }

    setMessages((prev) => [...prev, userMessage])
    setInputValue("")
    setIsTyping(true)

    // Simulate AI response
    setTimeout(
      () => {
        const aiMessage: Message = {
          id: (Date.now() + 1).toString(),
          content: `I understand you said: "${userMessage.content}". This is a simulated response. How else can I help you?`,
          sender: "ai",
          timestamp: new Date(),
        }
        setMessages((prev) => [...prev, aiMessage])
        setIsTyping(false)
      },
      1000 + Math.random() * 2000,
    )
  }

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === "Enter" && !e.shiftKey) {
      e.preventDefault()
      handleSendMessage()
    }
  }

  const startVoiceConversation = async () => {
    const hasPermission = await requestMicrophonePermission()
    if (!hasPermission) {
      alert("Microphone permission is required for voice calls")
      return
    }
    try {
      const signedUrl = await getSignedUrl()
      const conversationId = await conversation.startSession({ signedUrl })
      console.log("Voice conversation started:", conversationId)
    } catch (error) {
      console.error("Failed to start voice conversation:", error)
      alert("Failed to start voice conversation. Please try again.")
    }
  }

  const stopVoiceConversation = async () => {
    await conversation.endSession()
  }

  return (
    <>
      {/* Floating Action Button */}
      <div className="fixed bottom-6 right-6 z-50">
        <Button
          onClick={handleMainButtonClick}
          className="w-14 h-14 rounded-full bg-blue-600 hover:bg-blue-700 text-white shadow-lg hover:scale-105 transition-all duration-300"
          size="icon"
        >
          <MessageCircle className="w-6 h-6" />
        </Button>

        {/* Options Menu */}
        <div
          className={cn(
            "absolute bottom-16 right-0 flex flex-col gap-3 transition-all duration-300",
            isOptionsOpen
              ? "opacity-100 translate-y-0 pointer-events-auto"
              : "opacity-0 translate-y-4 pointer-events-none",
          )}
        >
          <Button
            onClick={handleTextChatClick}
            className="px-6 py-3 rounded-full bg-blue-600 hover:bg-blue-700 text-white shadow-lg hover:-translate-x-2 transition-all duration-300 min-w-[100px]"
          >
            Chat
          </Button>
          <Button
            onClick={handleVoiceCallClick}
            className="px-6 py-3 rounded-full bg-blue-600 hover:bg-blue-700 text-white shadow-lg hover:-translate-x-2 transition-all duration-300 min-w-[100px]"
          >
            Call
          </Button>
        </div>
      </div>

      {/* Voice Call Dialog */}
      {isVoiceCallOpen && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-[100] p-4">
          <Card className="w-full max-w-md bg-white rounded-2xl shadow-2xl">
            <CardHeader className="relative pb-4">
              <Button
                onClick={handleCloseVoiceCall}
                variant="ghost"
                size="icon"
                className="absolute top-2 right-2 w-8 h-8 rounded-full hover:bg-gray-100"
              >
                <X className="w-4 h-4" />
              </Button>
              <CardTitle className="text-center text-xl font-semibold">Voice Call</CardTitle>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="text-center">
                <div className="text-lg font-medium mb-2">
                  {conversation.status === "connected"
                    ? conversation.isSpeaking
                      ? "AI is speaking..."
                      : "AI is listening..."
                    : "Ready to start"}
                </div>
                <div className="text-sm text-gray-500">
                  {conversation.status === "connected" ? "Connected" : "Disconnected"}
                </div>
              </div>

              {/* Voice Orb */}
              <div className="flex justify-center">
                <div
                  className={cn(
                    "w-32 h-32 rounded-full transition-all duration-300",
                    conversation.status === "connected" && conversation.isSpeaking
                      ? "bg-gradient-to-r from-blue-500 to-purple-600 animate-pulse scale-110"
                      : conversation.status === "connected"
                        ? "bg-gradient-to-r from-green-500 to-teal-600 animate-pulse"
                        : "bg-gradient-to-r from-gray-400 to-gray-500",
                  )}
                />
              </div>

              {/* Voice Controls */}
              <div className="flex justify-center gap-4">
                <Button
                  onClick={startVoiceConversation}
                  disabled={conversation.status === "connected"}
                  className="flex items-center gap-2 bg-green-600 hover:bg-green-700 disabled:opacity-50"
                >
                  <Mic className="w-4 h-4" />
                  Start Call
                </Button>
                <Button
                  onClick={stopVoiceConversation}
                  disabled={conversation.status !== "connected"}
                  variant="destructive"
                  className="flex items-center gap-2 disabled:opacity-50"
                >
                  <MicOff className="w-4 h-4" />
                  End Call
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Text Chat Dialog */}
      {isTextChatOpen && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-[100] p-4">
          <Card className="w-full max-w-md h-[600px] bg-white rounded-2xl shadow-2xl flex flex-col">
            <CardHeader className="relative pb-4 flex-shrink-0">
              <Button
                onClick={handleCloseTextChat}
                variant="ghost"
                size="icon"
                className="absolute top-2 right-2 w-8 h-8 rounded-full hover:bg-gray-100"
              >
                <X className="w-4 h-4" />
              </Button>
              <CardTitle className="text-center text-xl font-semibold">Text Chat</CardTitle>
            </CardHeader>

            {/* Messages */}
            <div className="flex-1 overflow-y-auto p-4 space-y-4">
              {messages.map((message) => (
                <div
                  key={message.id}
                  className={cn(
                    "flex items-start space-x-3 max-w-[85%]",
                    message.sender === "user" ? "ml-auto flex-row-reverse space-x-reverse" : "",
                  )}
                >
                  <div
                    className={cn(
                      "flex-shrink-0 w-8 h-8 rounded-full flex items-center justify-center",
                      message.sender === "user"
                        ? "bg-gradient-to-r from-blue-500 to-purple-600"
                        : "bg-gradient-to-r from-green-500 to-teal-600",
                    )}
                  >
                    {message.sender === "user" ? (
                      <User className="w-4 h-4 text-white" />
                    ) : (
                      <Bot className="w-4 h-4 text-white" />
                    )}
                  </div>
                  <div
                    className={cn(
                      "p-3 rounded-2xl max-w-full",
                      message.sender === "user" ? "bg-blue-600 text-white" : "bg-gray-100 text-gray-900",
                    )}
                  >
                    <p className="text-sm leading-relaxed">{message.content}</p>
                    <p className="text-xs opacity-70 mt-1">
                      {message.timestamp.toLocaleTimeString([], {
                        hour: "2-digit",
                        minute: "2-digit",
                      })}
                    </p>
                  </div>
                </div>
              ))}

              {isTyping && (
                <div className="flex items-start space-x-3 max-w-[85%]">
                  <div className="flex-shrink-0 w-8 h-8 rounded-full bg-gradient-to-r from-green-500 to-teal-600 flex items-center justify-center">
                    <Bot className="w-4 h-4 text-white" />
                  </div>
                  <div className="p-3 rounded-2xl bg-gray-100">
                    <div className="flex space-x-1">
                      <div className="w-2 h-2 bg-gray-500 rounded-full animate-bounce" />
                      <div className="w-2 h-2 bg-gray-500 rounded-full animate-bounce animation-delay-200" />
                      <div className="w-2 h-2 bg-gray-500 rounded-full animate-bounce animation-delay-400" />
                    </div>
                  </div>
                </div>
              )}
              <div ref={messagesEndRef} />
            </div>

            {/* Input */}
            <div className="p-4 border-t flex-shrink-0">
              <div className="flex space-x-2">
                <Input
                  value={inputValue}
                  onChange={(e) => setInputValue(e.target.value)}
                  onKeyPress={handleKeyPress}
                  placeholder="Type your message..."
                  className="flex-1"
                  disabled={isTyping}
                />
                <Button
                  onClick={handleSendMessage}
                  disabled={!inputValue.trim() || isTyping}
                  className="bg-blue-600 hover:bg-blue-700"
                  size="icon"
                >
                  <Send className="w-4 h-4" />
                </Button>
              </div>
            </div>
          </Card>
        </div>
      )}
    </>
  )
}
