"use client"

import { useState, useEffect } from "react"
import { X, Phone, PhoneOff, Mic, MicOff, Volume2, VolumeX, Video, VideoOff } from "lucide-react"
import { But<PERSON> } from "@/components/ui/button"
import { cn } from "@/lib/utils"

interface CallDialogProps {
  isOpen: boolean
  onClose: () => void
}

export function CallDialog({ isOpen, onClose }: CallDialogProps) {
  const [isConnected, setIsConnected] = useState(false)
  const [isMuted, setIsMuted] = useState(false)
  const [isSpeakerOn, setIsSpeakerOn] = useState(true)
  const [isVideoOn, setIsVideoOn] = useState(false)
  const [callDuration, setCallDuration] = useState(0)
  const [isAISpeaking, setIsAISpeaking] = useState(false)

  useEffect(() => {
    let interval: NodeJS.Timeout
    if (isConnected) {
      interval = setInterval(() => {
        setCallDuration((prev) => prev + 1)
      }, 1000)
    }
    return () => clearInterval(interval)
  }, [isConnected])

  useEffect(() => {
    if (isConnected) {
      // Simulate AI speaking patterns
      const speakingInterval = setInterval(
        () => {
          setIsAISpeaking(true)
          setTimeout(() => setIsAISpeaking(false), 2000 + Math.random() * 3000)
        },
        5000 + Math.random() * 5000,
      )

      return () => clearInterval(speakingInterval)
    }
  }, [isConnected])

  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60)
    const secs = seconds % 60
    return `${mins.toString().padStart(2, "0")}:${secs.toString().padStart(2, "0")}`
  }

  const handleStartCall = () => {
    setIsConnected(true)
    setCallDuration(0)
  }

  const handleEndCall = () => {
    setIsConnected(false)
    setCallDuration(0)
    setIsAISpeaking(false)
    onClose()
  }

  if (!isOpen) return null

  return (
    <div className="fixed inset-0 bg-black/80 backdrop-blur-sm flex items-center justify-center z-[100] p-4">
      <div className="w-full max-w-md h-[600px] bg-gradient-to-br from-slate-900 via-purple-900/50 to-slate-900 rounded-3xl shadow-2xl border border-white/10 flex flex-col overflow-hidden animate-in fade-in-0 zoom-in-95 duration-300">
        {/* Header */}
        <div className="relative p-6 text-white">
          <div className="absolute inset-0 bg-gradient-to-r from-purple-600/20 via-pink-600/20 to-blue-600/20 backdrop-blur-sm" />
          <div className="relative flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <div className="text-lg font-semibold">AI Assistant</div>
              {isConnected && (
                <div className="px-3 py-1 bg-green-500/20 rounded-full text-green-300 text-sm font-medium">
                  {formatTime(callDuration)}
                </div>
              )}
            </div>
            <Button
              onClick={onClose}
              variant="ghost"
              size="icon"
              className="w-10 h-10 rounded-full hover:bg-white/10 text-white"
            >
              <X className="w-5 h-5" />
            </Button>
          </div>
        </div>

        {/* Avatar/Visual */}
        <div className="flex-1 flex items-center justify-center p-8">
          <div className="relative">
            {/* Main Avatar Circle */}
            <div
              className={cn(
                "w-48 h-48 rounded-full transition-all duration-500 flex items-center justify-center relative overflow-hidden",
                isConnected
                  ? isAISpeaking
                    ? "bg-gradient-to-r from-green-400 via-blue-500 to-purple-600 animate-pulse scale-110 shadow-2xl shadow-purple-500/50"
                    : "bg-gradient-to-r from-blue-500 via-purple-600 to-pink-600 shadow-xl shadow-purple-500/30"
                  : "bg-gradient-to-r from-gray-600 to-gray-700 shadow-lg",
              )}
            >
              {/* Animated rings */}
              {isConnected && (
                <>
                  <div className="absolute inset-0 rounded-full border-2 border-white/30 animate-ping" />
                  <div className="absolute inset-4 rounded-full border border-white/20 animate-pulse" />
                </>
              )}

              {/* AI Icon */}
              <div className="relative z-10">
                <div className="w-20 h-20 bg-white/20 rounded-full flex items-center justify-center backdrop-blur-sm">
                  <div className="text-4xl">🤖</div>
                </div>
              </div>

              {/* Floating particles */}
              {isConnected && (
                <div className="absolute inset-0 overflow-hidden">
                  {[...Array(12)].map((_, i) => (
                    <div
                      key={i}
                      className="absolute w-2 h-2 bg-white/40 rounded-full animate-float"
                      style={{
                        left: `${Math.random() * 100}%`,
                        top: `${Math.random() * 100}%`,
                        animationDelay: `${Math.random() * 3}s`,
                        animationDuration: `${2 + Math.random() * 2}s`,
                      }}
                    />
                  ))}
                </div>
              )}
            </div>

            {/* Status Text */}
            <div className="text-center mt-6 space-y-2">
              <div className="text-white text-xl font-semibold">
                {isConnected ? (isAISpeaking ? "AI is speaking..." : "Connected & Listening") : "Ready to connect"}
              </div>
              <div className="text-white/60 text-sm">{isConnected ? "Tap to interact" : "Start your conversation"}</div>
            </div>
          </div>
        </div>

        {/* Controls */}
        <div className="p-6 space-y-4">
          {/* Secondary Controls */}
          {isConnected && (
            <div className="flex justify-center space-x-4">
              <Button
                onClick={() => setIsMuted(!isMuted)}
                variant="ghost"
                size="icon"
                className={cn(
                  "w-12 h-12 rounded-full transition-all duration-300",
                  isMuted
                    ? "bg-red-500/20 text-red-400 hover:bg-red-500/30"
                    : "bg-white/10 text-white hover:bg-white/20",
                )}
              >
                {isMuted ? <MicOff className="w-5 h-5" /> : <Mic className="w-5 h-5" />}
              </Button>

              <Button
                onClick={() => setIsSpeakerOn(!isSpeakerOn)}
                variant="ghost"
                size="icon"
                className={cn(
                  "w-12 h-12 rounded-full transition-all duration-300",
                  !isSpeakerOn
                    ? "bg-yellow-500/20 text-yellow-400 hover:bg-yellow-500/30"
                    : "bg-white/10 text-white hover:bg-white/20",
                )}
              >
                {isSpeakerOn ? <Volume2 className="w-5 h-5" /> : <VolumeX className="w-5 h-5" />}
              </Button>

              <Button
                onClick={() => setIsVideoOn(!isVideoOn)}
                variant="ghost"
                size="icon"
                className={cn(
                  "w-12 h-12 rounded-full transition-all duration-300",
                  isVideoOn
                    ? "bg-blue-500/20 text-blue-400 hover:bg-blue-500/30"
                    : "bg-white/10 text-white hover:bg-white/20",
                )}
              >
                {isVideoOn ? <Video className="w-5 h-5" /> : <VideoOff className="w-5 h-5" />}
              </Button>
            </div>
          )}

          {/* Main Call Button */}
          <div className="flex justify-center">
            {!isConnected ? (
              <Button
                onClick={handleStartCall}
                className="w-16 h-16 rounded-full bg-gradient-to-r from-green-500 to-emerald-600 hover:from-green-600 hover:to-emerald-700 shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-110"
                size="icon"
              >
                <Phone className="w-8 h-8" />
              </Button>
            ) : (
              <Button
                onClick={handleEndCall}
                className="w-16 h-16 rounded-full bg-gradient-to-r from-red-500 to-rose-600 hover:from-red-600 hover:to-rose-700 shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-110"
                size="icon"
              >
                <PhoneOff className="w-8 h-8" />
              </Button>
            )}
          </div>
        </div>
      </div>
    </div>
  )
}
