"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { useCallback } from "react"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { useConversation } from "@11labs/react"
import { cn } from "@/lib/utils"

async function requestMicrophonePermission() {
  try {
    await navigator.mediaDevices.getUserMedia({ audio: true })
    return true
  } catch {
    console.error("Microphone permission denied")
    return false
  }
}

async function getSignedUrl(): Promise<string> {
  const response = await fetch("/api/signed-url")
  if (!response.ok) {
    throw Error("Failed to get signed url")
  }
  const data = await response.json()
  return data.signedUrl
}

export function ConvAI() {
  const conversation = useConversation({
    onConnect: () => {
      console.log("connected")
    },
    onDisconnect: () => {
      console.log("disconnected")
    },
    onError: (error) => {
      console.log(error)
      alert("An error occurred during the conversation")
    },
    onMessage: (message) => {
      console.log(message)
    },
  })

  async function startConversation() {
    const hasPermission = await requestMicrophonePermission()
    if (!hasPermission) {
      alert("No permission")
      return
    }
    const signedUrl = await getSignedUrl()
    const conversationId = await conversation.startSession({ signedUrl })
    console.log(conversationId)
  }

  const stopConversation = useCallback(async () => {
    await conversation.endSession()
  }, [conversation])

  return (
    <div className={"flex justify-center items-center gap-x-4"}>
      <Card className={"rounded-3xl"}>
        <CardContent>
          <CardHeader>
            <CardTitle className={"text-center"}>
              {conversation.status === "connected"
                ? conversation.isSpeaking
                  ? `Agent is speaking`
                  : "Agent is listening"
                : "Disconnected"}
            </CardTitle>
          </CardHeader>
          <div className={"flex flex-col gap-y-4 text-center"}>
            <div
              className={cn(
                "orb my-16 mx-12",
                conversation.status === "connected" && conversation.isSpeaking
                  ? "orb-active animate-orb"
                  : conversation.status === "connected"
                    ? "animate-orb-slow orb-inactive"
                    : "orb-inactive",
              )}
            ></div>

            <Button
              variant={"outline"}
              className={"rounded-full"}
              size={"lg"}
              disabled={conversation !== null && conversation.status === "connected"}
              onClick={startConversation}
            >
              Start conversation
            </Button>
            <Button
              variant={"outline"}
              className={"rounded-full"}
              size={"lg"}
              disabled={conversation === null}
              onClick={stopConversation}
            >
              End conversation
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
