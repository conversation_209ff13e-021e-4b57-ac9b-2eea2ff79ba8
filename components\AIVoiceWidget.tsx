"use client"

import type React from "react"

import { useState, useEffect } from "react"
import { MessageCircle, X } from "lucide-react"
import { Button } from "@/components/ui/button"
import { cn } from "@/lib/utils"

declare global {
  namespace JSX {
    interface IntrinsicElements {
      "elevenlabs-convai": {
        "agent-id": string
        children?: React.ReactNode
      }
    }
  }
}

export function AIVoiceWidget() {
  const [isOptionsOpen, setIsOptionsOpen] = useState(false)
  const [activeChatAgent, setActiveChatAgent] = useState(false)
  const [activeCallAgent, setActiveCallAgent] = useState(false)

  useEffect(() => {
    // Load ElevenLabs ConvAI widget script
    const script = document.createElement("script")
    script.src = "https://unpkg.com/@elevenlabs/convai-widget-embed"
    script.async = true
    script.type = "text/javascript"
    document.head.appendChild(script)

    return () => {
      document.head.removeChild(script)
    }
  }, [])

  const handleMainButtonClick = () => {
    setIsOptionsOpen(!isOptionsOpen)
  }

  const handleChatClick = () => {
    setActiveChatAgent(true)
    setIsOptionsOpen(false)
    setActiveCallAgent(false)
  }

  const handleCallClick = () => {
    setActiveCallAgent(true)
    setIsOptionsOpen(false)
    setActiveChatAgent(false)
  }

  const handleCloseChatAgent = () => {
    setActiveChatAgent(false)
  }

  const handleCloseCallAgent = () => {
    setActiveCallAgent(false)
  }

  return (
    <div className="fixed inset-0 overflow-hidden">
      {/* Animated Background */}
      <div className="absolute inset-0 bg-gradient-to-br from-blue-900 via-red-800 to-yellow-600 bg-[length:400%_400%] animate-gradient-shift -z-20" />

      {/* Animated Stars */}
      <div
        className="absolute inset-0 opacity-80 -z-10 animate-stars-move"
        style={{
          backgroundImage: `
            radial-gradient(2px 2px at 20% 30%, white, transparent),
            radial-gradient(2px 2px at 60% 70%, white, transparent),
            radial-gradient(1px 1px at 50% 50%, white, transparent)
          `,
          backgroundSize: "200px 200px",
        }}
      />

      {/* Main Content */}
      <div className="flex flex-col justify-center items-center h-screen text-center px-5 relative z-10">
        <h1 className="text-5xl md:text-8xl font-bold text-white mb-6 tracking-wider animate-glow">
          AI Voice Experience
        </h1>
        <p className="text-xl md:text-2xl font-light text-white/90 max-w-4xl leading-relaxed">
          Converse with our advanced AI assistant powered by ElevenLabs technology
        </p>
      </div>

      {/* Floating Action Button */}
      <div className="fixed bottom-8 right-8 z-[1000]">
        <Button
          onClick={handleMainButtonClick}
          className="w-15 h-15 rounded-full bg-blue-600 hover:bg-blue-700 text-white shadow-lg hover:scale-105 transition-all duration-300"
          size="icon"
        >
          <MessageCircle className="w-6 h-6" />
        </Button>

        {/* Options Menu */}
        <div
          className={cn(
            "absolute bottom-[70px] right-0 flex flex-col gap-4 transition-all duration-300",
            isOptionsOpen
              ? "opacity-100 translate-y-0 pointer-events-auto"
              : "opacity-0 translate-y-5 pointer-events-none",
          )}
        >
          <Button
            onClick={handleChatClick}
            className="px-6 py-3 rounded-full bg-blue-600 hover:bg-blue-700 text-white shadow-lg hover:-translate-x-1 transition-all duration-300 min-w-[120px]"
          >
            Chat
          </Button>
          <Button
            onClick={handleCallClick}
            className="px-6 py-3 rounded-full bg-blue-600 hover:bg-blue-700 text-white shadow-lg hover:-translate-x-1 transition-all duration-300 min-w-[120px]"
          >
            Call
          </Button>
        </div>
      </div>

      {/* Chat Agent Container */}
      <div
        className={cn(
          "fixed bottom-[100px] right-8 w-[400px] h-[600px] bg-white rounded-2xl shadow-2xl z-[999] overflow-hidden transition-all duration-300",
          "max-md:w-[90%] max-md:h-[70%] max-md:right-[5%] max-md:left-[5%]",
          activeChatAgent ? "scale-100" : "scale-0",
        )}
      >
        <Button
          onClick={handleCloseChatAgent}
          variant="ghost"
          size="icon"
          className="absolute top-4 right-4 w-8 h-8 rounded-full bg-black/10 hover:bg-black/20 text-gray-600 hover:text-black z-[1001]"
        >
          <X className="w-4 h-4" />
        </Button>
        <elevenlabs-convai agent-id="agent_7001k1h6an4eeqf9gem3jnb4ypks" />
      </div>

      {/* Call Agent Container */}
      <div
        className={cn(
          "fixed bottom-[100px] right-8 w-[400px] h-[600px] bg-white rounded-2xl shadow-2xl z-[999] overflow-hidden transition-all duration-300",
          "max-md:w-[90%] max-md:h-[70%] max-md:right-[5%] max-md:left-[5%]",
          activeCallAgent ? "scale-100" : "scale-0",
        )}
      >
        <Button
          onClick={handleCloseCallAgent}
          variant="ghost"
          size="icon"
          className="absolute top-4 right-4 w-8 h-8 rounded-full bg-black/10 hover:bg-black/20 text-gray-600 hover:text-black z-[1001]"
        >
          <X className="w-4 h-4" />
        </Button>
        <elevenlabs-convai agent-id="agent_2801k19bqpw9ffqryy9c1zd4mwv7" />
      </div>
    </div>
  )
}
