import { ConvAI } from "@/components/ConvAI"
import Link from "next/link"
import { ArrowLeft } from "lucide-react"
import { But<PERSON> } from "@/components/ui/button"

export default function CallPage() {
  return (
    <div className="min-h-screen relative">
      {/* Back button */}
      <div className="absolute top-4 left-4 z-20">
        <Link href="/">
          <Button variant="ghost" className="text-white hover:bg-white/10">
            <ArrowLeft className="w-4 h-4 mr-2" />
            Back
          </Button>
        </Link>
      </div>

      <div className="grid grid-rows-[20px_1fr_20px] items-center justify-items-center min-h-screen p-8 pb-20 gap-16 sm:p-20 font-[family-name:var(--font-geist-sans)]">
        <main className="flex flex-col gap-8 row-start-2 items-center">
          <div className="text-center space-y-4 mb-8">
            <h1 className="text-4xl md:text-6xl font-bold bg-gradient-to-r from-white via-blue-100 to-purple-100 bg-clip-text text-transparent">
              Voice Conversation
            </h1>
            <p className="text-gray-400 text-lg">Speak naturally with our AI assistant</p>
          </div>
          <ConvAI />
        </main>
      </div>
    </div>
  )
}
